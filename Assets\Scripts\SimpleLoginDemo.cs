using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class SimpleLoginDemo : MonoBehaviour
{
    [Header("请在Inspector中拖拽分配这些组件")]
    public InputField usernameInput;
    public InputField passwordInput;
    public Button loginButton;
    public Text welcomeText;
    
    [Header("登录设置")]
    public string correctUsername = "admin";
    public string correctPassword = "123456";
    public string nextSceneName = "MainScene";
    
    void Start()
    {
        // 设置按钮点击事件
        if (loginButton != null)
        {
            loginButton.onClick.AddListener(OnLoginClick);
        }
        
        // 设置默认焦点到用户名输入框
        if (usernameInput != null)
        {
            usernameInput.Select();
        }
    }
    
    public void OnLoginClick()
    {
        // 获取输入的用户名和密码
        string username = usernameInput != null ? usernameInput.text : "";
        string password = passwordInput != null ? passwordInput.text : "";
        
        // 验证输入
        if (string.IsNullOrEmpty(username))
        {
            ShowMessage("请输入用户名", Color.red);
            return;
        }
        
        if (string.IsNullOrEmpty(password))
        {
            ShowMessage("请输入密码", Color.red);
            return;
        }
        
        // 验证凭据
        if (username == correctUsername && password == correctPassword)
        {
            ShowMessage("登录成功!", Color.green);
            
            // 延迟跳转到下一个场景
            Invoke("LoadNextScene", 1.0f);
        }
        else
        {
            ShowMessage("用户名或密码错误", Color.red);
            
            // 清空密码框
            if (passwordInput != null)
            {
                passwordInput.text = "";
                passwordInput.Select();
            }
        }
    }
    
    void ShowMessage(string message, Color color)
    {
        if (welcomeText != null)
        {
            welcomeText.text = message;
            welcomeText.color = color;
            
            // 2秒后恢复原始文本
            Invoke("RestoreWelcomeText", 2.0f);
        }
        
        Debug.Log($"Login: {message}");
    }
    
    void RestoreWelcomeText()
    {
        if (welcomeText != null)
        {
            welcomeText.text = "Welcome back";
            welcomeText.color = new Color(0.2f, 0.2f, 0.2f, 1.0f);
        }
    }
    
    void LoadNextScene()
    {
        if (!string.IsNullOrEmpty(nextSceneName))
        {
            SceneManager.LoadScene(nextSceneName);
        }
        else
        {
            Debug.Log("登录成功! 请在Inspector中设置下一个场景名称。");
        }
    }
    
    void Update()
    {
        // 支持回车键登录
        if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
        {
            OnLoginClick();
        }
        
        // 支持Tab键切换输入框
        if (Input.GetKeyDown(KeyCode.Tab))
        {
            if (usernameInput != null && usernameInput.isFocused)
            {
                if (passwordInput != null)
                    passwordInput.Select();
            }
            else if (passwordInput != null && passwordInput.isFocused)
            {
                if (usernameInput != null)
                    usernameInput.Select();
            }
        }
    }
}
