using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using System.Collections;

public class LoginManager : MonoBehaviour
{
    [Header("UI References")]
    public InputField usernameInputField;
    public InputField passwordInputField;
    public Button loginButton;
    public Text welcomeText;
    public Image avatarImage;
    public Text signupText;
    
    [Header("Login Settings")]
    public string nextSceneName = "MainScene";
    public float loginAnimationDuration = 1.0f;
    
    [Header("Demo Credentials")]
    public string demoUsername = "admin";
    public string demoPassword = "123456";
    
    private bool isLoggingIn = false;
    
    void Start()
    {
        // 设置按钮点击事件
        if (loginButton != null)
        {
            loginButton.onClick.AddListener(OnLoginButtonClicked);
        }
        
        // 设置输入框回车事件
        if (passwordInputField != null)
        {
            passwordInputField.onEndEdit.AddListener(OnPasswordEndEdit);
        }
        
        // 设置头像为圆形
        SetupAvatarImage();
        
        // 设置注册文本点击事件
        SetupSignupText();
        
        // 播放欢迎动画
        StartCoroutine(PlayWelcomeAnimation());
    }
    
    void SetupAvatarImage()
    {
        if (avatarImage != null)
        {
            // 设置头像背景色（蓝色渐变效果）
            avatarImage.color = new Color(0.3f, 0.7f, 1.0f, 1.0f);
        }
    }
    
    void SetupSignupText()
    {
        if (signupText != null)
        {
            // 添加点击检测
            Button signupButton = signupText.gameObject.AddComponent<Button>();
            signupButton.onClick.AddListener(OnSignupClicked);
            
            // 设置颜色变化
            ColorBlock colors = signupButton.colors;
            colors.normalColor = new Color(0.5f, 0.5f, 0.5f, 1.0f);
            colors.highlightedColor = new Color(0.2f, 0.6f, 1.0f, 1.0f);
            colors.pressedColor = new Color(0.1f, 0.4f, 0.8f, 1.0f);
            signupButton.colors = colors;
        }
    }
    
    IEnumerator PlayWelcomeAnimation()
    {
        // 初始化UI元素为透明
        CanvasGroup panelGroup = GetComponent<CanvasGroup>();
        if (panelGroup == null)
        {
            panelGroup = gameObject.AddComponent<CanvasGroup>();
        }
        
        panelGroup.alpha = 0f;
        
        // 淡入动画
        float elapsedTime = 0f;
        while (elapsedTime < loginAnimationDuration)
        {
            elapsedTime += Time.deltaTime;
            panelGroup.alpha = Mathf.Lerp(0f, 1f, elapsedTime / loginAnimationDuration);
            yield return null;
        }
        
        panelGroup.alpha = 1f;
    }
    
    void OnPasswordEndEdit(string password)
    {
        // 当在密码框按回车时，尝试登录
        if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
        {
            OnLoginButtonClicked();
        }
    }
    
    public void OnLoginButtonClicked()
    {
        if (isLoggingIn) return;
        
        string username = usernameInputField.text.Trim();
        string password = passwordInputField.text;
        
        // 验证输入
        if (string.IsNullOrEmpty(username))
        {
            ShowMessage("请输入用户名");
            return;
        }
        
        if (string.IsNullOrEmpty(password))
        {
            ShowMessage("请输入密码");
            return;
        }
        
        // 开始登录流程
        StartCoroutine(LoginProcess(username, password));
    }
    
    IEnumerator LoginProcess(string username, string password)
    {
        isLoggingIn = true;
        
        // 更新按钮状态
        UpdateLoginButtonState(false, "登录中...");
        
        // 模拟网络请求延迟
        yield return new WaitForSeconds(1.5f);
        
        // 验证凭据
        bool loginSuccess = ValidateCredentials(username, password);
        
        if (loginSuccess)
        {
            UpdateLoginButtonState(false, "登录成功!");
            yield return new WaitForSeconds(0.5f);
            
            // 播放成功动画并跳转场景
            yield return StartCoroutine(PlaySuccessAnimation());
            LoadNextScene();
        }
        else
        {
            UpdateLoginButtonState(true, "LOGIN");
            ShowMessage("用户名或密码错误");
            
            // 播放错误动画
            StartCoroutine(PlayErrorAnimation());
        }
        
        isLoggingIn = false;
    }
    
    bool ValidateCredentials(string username, string password)
    {
        // 简单的演示验证
        return username == demoUsername && password == demoPassword;
    }
    
    void UpdateLoginButtonState(bool interactable, string text)
    {
        if (loginButton != null)
        {
            loginButton.interactable = interactable;
            
            Text buttonText = loginButton.GetComponentInChildren<Text>();
            if (buttonText != null)
            {
                buttonText.text = text;
            }
        }
    }
    
    IEnumerator PlaySuccessAnimation()
    {
        // 成功时的绿色闪烁效果
        Image buttonImage = loginButton.GetComponent<Image>();
        Color originalColor = buttonImage.color;
        Color successColor = new Color(0.2f, 0.8f, 0.2f, 1.0f);
        
        float duration = 0.5f;
        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = Mathf.PingPong(elapsedTime * 4, 1);
            buttonImage.color = Color.Lerp(originalColor, successColor, t);
            yield return null;
        }
        
        buttonImage.color = successColor;
    }
    
    IEnumerator PlayErrorAnimation()
    {
        // 错误时的红色闪烁效果
        Image buttonImage = loginButton.GetComponent<Image>();
        Color originalColor = buttonImage.color;
        Color errorColor = new Color(1.0f, 0.3f, 0.3f, 1.0f);
        
        // 震动效果
        RectTransform buttonRect = loginButton.GetComponent<RectTransform>();
        Vector3 originalPosition = buttonRect.localPosition;
        
        float duration = 0.5f;
        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            
            // 颜色闪烁
            float colorT = Mathf.PingPong(elapsedTime * 6, 1);
            buttonImage.color = Color.Lerp(originalColor, errorColor, colorT);
            
            // 位置震动
            float shakeIntensity = 5f * (1 - elapsedTime / duration);
            buttonRect.localPosition = originalPosition + new Vector3(
                Random.Range(-shakeIntensity, shakeIntensity), 0, 0);
            
            yield return null;
        }
        
        buttonImage.color = originalColor;
        buttonRect.localPosition = originalPosition;
    }
    
    void ShowMessage(string message)
    {
        Debug.Log($"Login Message: {message}");
        StartCoroutine(ShowTemporaryMessage(message));
    }
    
    IEnumerator ShowTemporaryMessage(string message)
    {
        // 临时在欢迎文本上显示错误信息
        if (welcomeText != null)
        {
            string originalText = welcomeText.text;
            Color originalColor = welcomeText.color;
            
            welcomeText.text = message;
            welcomeText.color = Color.red;
            
            yield return new WaitForSeconds(2.0f);
            
            welcomeText.text = originalText;
            welcomeText.color = originalColor;
        }
    }
    
    public void OnSignupClicked()
    {
        Debug.Log("Signup clicked - 这里可以跳转到注册页面");
        ShowMessage("注册功能开发中...");
    }
    
    void LoadNextScene()
    {
        if (!string.IsNullOrEmpty(nextSceneName))
        {
            SceneManager.LoadScene(nextSceneName);
        }
        else
        {
            Debug.Log("登录成功! 请设置下一个场景名称。");
        }
    }
    
    void Update()
    {
        // 支持Tab键切换输入框
        if (Input.GetKeyDown(KeyCode.Tab))
        {
            if (usernameInputField.isFocused)
            {
                passwordInputField.Select();
            }
            else if (passwordInputField.isFocused)
            {
                usernameInputField.Select();
            }
        }
    }
    
    // 公共方法供外部调用
    public void SetCredentials(string username, string password)
    {
        if (usernameInputField != null) usernameInputField.text = username;
        if (passwordInputField != null) passwordInputField.text = password;
    }
    
    public void ClearInputs()
    {
        if (usernameInputField != null) usernameInputField.text = "";
        if (passwordInputField != null) passwordInputField.text = "";
    }
}
