{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 16720, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 16720, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 16720, "tid": 18, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 16720, "tid": 18, "ts": 1748340548436966, "dur": 515, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 16720, "tid": 18, "ts": 1748340548439362, "dur": 883, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 16720, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 16720, "tid": 1, "ts": 1748340548209518, "dur": 4372, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 16720, "tid": 1, "ts": 1748340548213896, "dur": 60219, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 16720, "tid": 1, "ts": 1748340548274135, "dur": 66535, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 16720, "tid": 18, "ts": 1748340548440253, "dur": 291, "ph": "X", "name": "", "args": {}}, {"pid": 16720, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548207834, "dur": 5611, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548213449, "dur": 214857, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548214094, "dur": 2527, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548216634, "dur": 1290, "ph": "X", "name": "ProcessMessages 20483", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548217932, "dur": 213, "ph": "X", "name": "ReadAsync 20483", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218149, "dur": 24, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218176, "dur": 27, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218205, "dur": 2, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218209, "dur": 20, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218230, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218233, "dur": 15, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218250, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218252, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218273, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218295, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218298, "dur": 12, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218314, "dur": 26, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218344, "dur": 329, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218681, "dur": 5, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218689, "dur": 89, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218780, "dur": 10, "ph": "X", "name": "ProcessMessages 7830", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218792, "dur": 13, "ph": "X", "name": "ReadAsync 7830", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218807, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218809, "dur": 9, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218822, "dur": 7, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218832, "dur": 7, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218842, "dur": 7, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218852, "dur": 8, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218863, "dur": 7, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218872, "dur": 5, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218881, "dur": 7, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218890, "dur": 6, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218899, "dur": 6, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218909, "dur": 18, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218930, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218943, "dur": 7, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218950, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218953, "dur": 24, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218980, "dur": 9, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548218992, "dur": 7, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219001, "dur": 8, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219012, "dur": 8, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219023, "dur": 24, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219050, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219064, "dur": 2, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219067, "dur": 7, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219078, "dur": 7, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219088, "dur": 8, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219099, "dur": 8, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219110, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219130, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219142, "dur": 36, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219180, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219182, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219196, "dur": 7, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219206, "dur": 6, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219215, "dur": 7, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219224, "dur": 4, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219232, "dur": 156, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219395, "dur": 7, "ph": "X", "name": "ProcessMessages 3394", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219405, "dur": 23, "ph": "X", "name": "ReadAsync 3394", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219431, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219434, "dur": 27, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219464, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219468, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219491, "dur": 2, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219495, "dur": 51, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219553, "dur": 4, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219560, "dur": 65, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219633, "dur": 5, "ph": "X", "name": "ProcessMessages 1169", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219642, "dur": 36, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219682, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219687, "dur": 28, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219719, "dur": 3, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219723, "dur": 17, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219744, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219748, "dur": 180, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219935, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219940, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219983, "dur": 3, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548219988, "dur": 15, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220007, "dur": 10, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220022, "dur": 8, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220034, "dur": 6, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220043, "dur": 33, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220080, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220091, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220094, "dur": 27, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220122, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220124, "dur": 8, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220136, "dur": 7, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220146, "dur": 7, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220156, "dur": 7, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220165, "dur": 5, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220173, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220192, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220204, "dur": 6, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220213, "dur": 7, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220222, "dur": 13, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220237, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220238, "dur": 8, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220249, "dur": 7, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220258, "dur": 7, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220268, "dur": 5, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220277, "dur": 17, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220297, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220309, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220311, "dur": 7, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220322, "dur": 8, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220333, "dur": 8, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220344, "dur": 7, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220355, "dur": 7, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220365, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220388, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220400, "dur": 8, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220411, "dur": 12, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220424, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220426, "dur": 7, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220437, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220459, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220461, "dur": 28, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220496, "dur": 4, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220503, "dur": 246, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220753, "dur": 3, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220759, "dur": 76, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220839, "dur": 10, "ph": "X", "name": "ProcessMessages 7076", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220851, "dur": 22, "ph": "X", "name": "ReadAsync 7076", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220874, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220877, "dur": 23, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220904, "dur": 15, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220921, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220923, "dur": 16, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220941, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220943, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220962, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220964, "dur": 16, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220982, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548220984, "dur": 14, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221000, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221002, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221020, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221044, "dur": 16, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221062, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221065, "dur": 23, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221090, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221092, "dur": 15, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221111, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221130, "dur": 19, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221150, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221153, "dur": 15, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221169, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221172, "dur": 16, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221190, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221192, "dur": 14, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221209, "dur": 16, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221226, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221229, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221246, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221248, "dur": 41, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221293, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221298, "dur": 18, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221317, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221320, "dur": 37, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221360, "dur": 2, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221363, "dur": 13, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221380, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221406, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221412, "dur": 20, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221434, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221436, "dur": 24, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221462, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221465, "dur": 24, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221491, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221493, "dur": 56, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221553, "dur": 3, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221558, "dur": 32, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221595, "dur": 4, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221601, "dur": 33, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221638, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221689, "dur": 3, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221695, "dur": 27, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221723, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221726, "dur": 18, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221746, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221749, "dur": 21, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221774, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221794, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221797, "dur": 17, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221815, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221817, "dur": 16, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221835, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221837, "dur": 13, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221854, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221875, "dur": 29, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221908, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221931, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221933, "dur": 16, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221951, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221953, "dur": 14, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221969, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221972, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221989, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548221991, "dur": 16, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222009, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222011, "dur": 16, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222029, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222031, "dur": 37, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222070, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222072, "dur": 35, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222108, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222110, "dur": 24, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222136, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222138, "dur": 16, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222156, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222158, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222175, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222177, "dur": 26, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222205, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222207, "dur": 22, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222231, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222233, "dur": 16, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222251, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222253, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222271, "dur": 15, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222289, "dur": 15, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222305, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222308, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222326, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222328, "dur": 16, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222346, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222348, "dur": 14, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222364, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222367, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222387, "dur": 16, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222405, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222407, "dur": 15, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222425, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222427, "dur": 16, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222445, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222447, "dur": 56, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222506, "dur": 17, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222525, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222528, "dur": 18, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222547, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222549, "dur": 17, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222568, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222570, "dur": 14, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222588, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222606, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222608, "dur": 16, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222626, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222628, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222646, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222648, "dur": 14, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222666, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222685, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222687, "dur": 17, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222706, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222708, "dur": 16, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222727, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222729, "dur": 14, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222747, "dur": 19, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222767, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222770, "dur": 15, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222786, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222788, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222806, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222808, "dur": 14, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222826, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222849, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222851, "dur": 16, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222915, "dur": 25, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222942, "dur": 2, "ph": "X", "name": "ProcessMessages 1717", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222946, "dur": 21, "ph": "X", "name": "ReadAsync 1717", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222969, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222971, "dur": 16, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222989, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548222992, "dur": 24, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223019, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223038, "dur": 16, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223056, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223059, "dur": 16, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223077, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223079, "dur": 16, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223097, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223099, "dur": 14, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548223117, "dur": 996, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224120, "dur": 4, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224127, "dur": 144, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224276, "dur": 21, "ph": "X", "name": "ProcessMessages 15221", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224299, "dur": 102, "ph": "X", "name": "ReadAsync 15221", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224407, "dur": 4, "ph": "X", "name": "ProcessMessages 1256", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224413, "dur": 33, "ph": "X", "name": "ReadAsync 1256", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224448, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224452, "dur": 18, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224472, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224474, "dur": 72, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224550, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224583, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224586, "dur": 16, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224603, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224605, "dur": 70, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224678, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224697, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224700, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224719, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224721, "dur": 52, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224776, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224796, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224798, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224816, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224818, "dur": 53, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224874, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224892, "dur": 14, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224910, "dur": 16, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224928, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224930, "dur": 52, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548224985, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225002, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225004, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225020, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225022, "dur": 13, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225039, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225088, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225106, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225109, "dur": 17, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225127, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225129, "dur": 10, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225142, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225190, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225208, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225210, "dur": 17, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225230, "dur": 15, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225247, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225249, "dur": 60, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225312, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225330, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225333, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225350, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225353, "dur": 49, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225405, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225424, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225426, "dur": 16, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225443, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225445, "dur": 13, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225462, "dur": 42, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225507, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225526, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225529, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225547, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225549, "dur": 14, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225565, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225567, "dur": 12, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225582, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225626, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225645, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225647, "dur": 12, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225662, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225680, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225700, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225702, "dur": 21, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225725, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225727, "dur": 15, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225744, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225746, "dur": 14, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225765, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225815, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225833, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225836, "dur": 28, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225868, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225873, "dur": 42, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225918, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225937, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225940, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225957, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548225959, "dur": 51, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226013, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226032, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226034, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226053, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226055, "dur": 44, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226102, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226120, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226122, "dur": 15, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226139, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226141, "dur": 13, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226157, "dur": 39, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226200, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226220, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226222, "dur": 16, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226240, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226242, "dur": 45, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226290, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226310, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226327, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226329, "dur": 13, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226346, "dur": 48, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226397, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226415, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226417, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226437, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226439, "dur": 12, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226455, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226493, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226513, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226515, "dur": 35, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226555, "dur": 3, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226561, "dur": 48, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226611, "dur": 2, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226614, "dur": 22, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226640, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226659, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226724, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226742, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226745, "dur": 22, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226768, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226770, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226794, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226796, "dur": 21, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226819, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226821, "dur": 21, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226844, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226846, "dur": 14, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226864, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226881, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226941, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226958, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226961, "dur": 15, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226977, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226980, "dur": 14, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548226997, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227048, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227065, "dur": 16, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227083, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227085, "dur": 14, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227101, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227104, "dur": 50, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227157, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227176, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227178, "dur": 16, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227195, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227197, "dur": 51, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227252, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227270, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227272, "dur": 16, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227290, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227292, "dur": 46, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227342, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227360, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227362, "dur": 17, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227381, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227383, "dur": 14, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227401, "dur": 44, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227449, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227466, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227469, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227486, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227489, "dur": 47, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227538, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227556, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227558, "dur": 16, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227576, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227578, "dur": 12, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227593, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227635, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227652, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227654, "dur": 16, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227673, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227675, "dur": 47, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227725, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227741, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227743, "dur": 14, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227760, "dur": 17, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227779, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227781, "dur": 48, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227834, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227853, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227855, "dur": 50, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227907, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227910, "dur": 44, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227957, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227975, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227977, "dur": 17, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227995, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548227998, "dur": 44, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228045, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228064, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228066, "dur": 14, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228083, "dur": 14, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228101, "dur": 38, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228142, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228161, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228178, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228180, "dur": 14, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228198, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228242, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228260, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228263, "dur": 16, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228280, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228283, "dur": 46, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228332, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228432, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228435, "dur": 30, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228469, "dur": 3, "ph": "X", "name": "ProcessMessages 1580", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228474, "dur": 16, "ph": "X", "name": "ReadAsync 1580", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228494, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228583, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228589, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228638, "dur": 4, "ph": "X", "name": "ProcessMessages 1394", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228645, "dur": 30, "ph": "X", "name": "ReadAsync 1394", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228679, "dur": 2, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228683, "dur": 17, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228703, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228707, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228727, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228731, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228788, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228808, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228811, "dur": 25, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228838, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228840, "dur": 15, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228858, "dur": 16, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228876, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228878, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228898, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228901, "dur": 15, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228920, "dur": 13, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228936, "dur": 44, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548228983, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229000, "dur": 111, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229119, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229125, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229197, "dur": 323, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229528, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229598, "dur": 9, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229609, "dur": 35, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229648, "dur": 3, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548229653, "dur": 515, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230175, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230180, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230309, "dur": 20, "ph": "X", "name": "ProcessMessages 1300", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230332, "dur": 106, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230447, "dur": 7, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230457, "dur": 101, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230564, "dur": 8, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230575, "dur": 45, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230622, "dur": 4, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230629, "dur": 39, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230676, "dur": 6, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230686, "dur": 47, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230741, "dur": 6, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230750, "dur": 176, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230935, "dur": 6, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548230944, "dur": 65, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231015, "dur": 11, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231029, "dur": 95, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231127, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231133, "dur": 40, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231177, "dur": 5, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231184, "dur": 48, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231240, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231249, "dur": 556, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231811, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231817, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231872, "dur": 7, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548231881, "dur": 161, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548232048, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548232052, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548232087, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548232090, "dur": 8640, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548240743, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548240752, "dur": 1028, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548241792, "dur": 7, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548241803, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548241846, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548241851, "dur": 1583, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548243443, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548243448, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548243542, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548243546, "dur": 183, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548243736, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548243741, "dur": 1204, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548244953, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548244961, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245141, "dur": 7, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245152, "dur": 60, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245222, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245230, "dur": 108, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245343, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245347, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245406, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245409, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245467, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245474, "dur": 162, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245642, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548245648, "dur": 458, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246110, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246116, "dur": 93, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246215, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246224, "dur": 39, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246265, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246268, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246306, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246313, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246342, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246347, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246382, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246386, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548246428, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247131, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247232, "dur": 7, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247243, "dur": 71, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247318, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247324, "dur": 220, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247548, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247551, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247629, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247634, "dur": 74, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247712, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247717, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247793, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247797, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247877, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247882, "dur": 70, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247956, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548247960, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248050, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248056, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248109, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248115, "dur": 107, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248231, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248237, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248278, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248282, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248317, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248322, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248360, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248365, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248399, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248403, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248439, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248442, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248476, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248481, "dur": 272, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248762, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248804, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248808, "dur": 110, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248927, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248932, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248973, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548248978, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548249004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548249007, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548249028, "dur": 797, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548249837, "dur": 84, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548249924, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548249930, "dur": 91, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548250025, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548250111, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548250117, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548250262, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548250266, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548250290, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548250293, "dur": 155512, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548405820, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548405826, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548405875, "dur": 1157, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548407040, "dur": 12330, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419385, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419393, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419476, "dur": 11, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419491, "dur": 31, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419525, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419529, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419548, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419551, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419582, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419590, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419620, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419624, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419646, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419648, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419704, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419709, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419738, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548419742, "dur": 440, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420184, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420188, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420322, "dur": 14, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420339, "dur": 42, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420383, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420386, "dur": 244, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420638, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420643, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420675, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420679, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420744, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420748, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420792, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420798, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420831, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420834, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420878, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420881, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420923, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420930, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420959, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548420962, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421040, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421065, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421068, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421095, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421097, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421124, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421239, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421245, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421293, "dur": 6, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421302, "dur": 295, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421605, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421634, "dur": 295, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 16720, "tid": 12884901888, "ts": 1748340548421933, "dur": 6266, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 16720, "tid": 18, "ts": 1748340548440547, "dur": 2211, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 16720, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 16720, "tid": 8589934592, "ts": 1748340548202651, "dur": 138051, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 16720, "tid": 8589934592, "ts": 1748340548340706, "dur": 12, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 16720, "tid": 8589934592, "ts": 1748340548340720, "dur": 1072, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 16720, "tid": 18, "ts": 1748340548442762, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 16720, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 16720, "tid": 4294967296, "ts": 1748340548177319, "dur": 251809, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 16720, "tid": 4294967296, "ts": 1748340548182999, "dur": 13827, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 16720, "tid": 4294967296, "ts": 1748340548429350, "dur": 5483, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 16720, "tid": 4294967296, "ts": 1748340548431335, "dur": 2319, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 16720, "tid": 4294967296, "ts": 1748340548434896, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 16720, "tid": 18, "ts": 1748340548442783, "dur": 16, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748340548211276, "dur": 1916, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548213204, "dur": 632, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548213959, "dur": 73, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748340548214032, "dur": 408, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548215410, "dur": 558, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EE0873F98CBCE221.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748340548217128, "dur": 1103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748340548218811, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748340548219678, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748340548220872, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748340548221606, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748340548224292, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748340548214461, "dur": 14626, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548229098, "dur": 192243, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548421342, "dur": 179, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548421613, "dur": 55, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548421688, "dur": 1359, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748340548214666, "dur": 14440, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548229129, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548229404, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_84B4700043351425.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748340548229472, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548229539, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BB9D23E252682F7E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748340548229596, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548229725, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548229908, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548230041, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548230137, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548230278, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548230587, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748340548230871, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548231280, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16765266549976182072.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748340548231367, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16765266549976182072.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748340548231558, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548232451, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548233086, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548233701, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548234298, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548234818, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548235280, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548235768, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548236631, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548237188, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548237707, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548238199, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548238733, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548239275, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548239761, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548240249, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548240718, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548241172, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548242091, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548242603, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548243048, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548243450, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548243840, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748340548244021, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548244122, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748340548244674, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548244984, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748340548245158, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748340548245958, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548246148, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548246391, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548246494, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548247154, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548248163, "dur": 162105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548410274, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748340548412661, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548413018, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748340548415263, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548415439, "dur": 2028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748340548417467, "dur": 1253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548418725, "dur": 2044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748340548420770, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748340548420854, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548215012, "dur": 14285, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548229307, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DC7478CF8F9290E4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548229395, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548229551, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_96F0F269DAA668CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548229611, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548229752, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548229898, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B1714AE5B82AAD0D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548230324, "dur": 397, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_B1714AE5B82AAD0D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548230733, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548230896, "dur": 9876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548240773, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548240907, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548241084, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548243459, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548243528, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548243816, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548244021, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548244688, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548244864, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548244963, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548245112, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548245362, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548246044, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548246552, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548246744, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548247687, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548247767, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548247864, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548248319, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548248413, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548249268, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548249348, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548249777, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748340548249851, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548250072, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548250301, "dur": 160048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548410350, "dur": 4337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548414687, "dur": 1277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548415974, "dur": 3871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748340548419846, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548420060, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548420273, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748340548420448, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548214957, "dur": 14324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548229297, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_8E612695A743588F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748340548229388, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548229680, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_67E562C08C9577A6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748340548229755, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548229831, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_2CCFBDF0A6E0168F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748340548229887, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548230303, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748340548230471, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748340548230525, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548231128, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748340548231183, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548231545, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548233231, "dur": 1208, "ph": "X", "name": "File", "args": {"detail": "D:\\Program Files\\Unity 2023.2.20f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-localization-l1-2-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748340548234440, "dur": 1429, "ph": "X", "name": "File", "args": {"detail": "D:\\Program Files\\Unity 2023.2.20f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748340548232567, "dur": 3467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548236034, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548237090, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548237786, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548238484, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548239125, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548239780, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548240400, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548240997, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548241560, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548242099, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548242671, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548243037, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548243475, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548243826, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748340548243980, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548244053, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748340548244662, "dur": 1499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548246167, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548246404, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548246513, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548247152, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748340548247339, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748340548247802, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548247884, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548248158, "dur": 96810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548344969, "dur": 65304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548410281, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748340548412568, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548412666, "dur": 2069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748340548414735, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548414966, "dur": 2164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748340548417130, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548417611, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748340548419892, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548419970, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548420283, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748340548421180, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548214675, "dur": 14443, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548229131, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548229258, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CB99E75C59157C3A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548229315, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548229381, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_378BE50CDB3A342E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548229487, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_AF917D20B118BC5A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548229734, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548229881, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_8BB421C3D827580F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548230037, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F291AD8967C736E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548230128, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548230525, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748340548230745, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748340548230802, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548230997, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548231599, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548232616, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548233368, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548234075, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548234822, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548235421, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548235996, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548236990, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548237524, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548238058, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548238587, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548239113, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548239591, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548240617, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.8.0\\Runtime\\VisualScripting.Core\\Reflection\\Namespace.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748340548240468, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548241657, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548242129, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548242900, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548243437, "dur": 1585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548245023, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748340548245672, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548246100, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548246187, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548246394, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548246487, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548246659, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748340548246946, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548247063, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548247159, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548248152, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548248371, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548248543, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748340548249081, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548249178, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748340548249317, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748340548249674, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548249757, "dur": 160525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548410284, "dur": 4298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748340548414583, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548414669, "dur": 3549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748340548418219, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748340548418406, "dur": 2797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748340548421288, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548214725, "dur": 14399, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548229131, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5808F3C777EC672D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748340548229244, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548229386, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_850897FB9E4DF4DA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748340548229470, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548229615, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_4DA32727832E2338.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748340548229678, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548229739, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_CE81BF96599B8CA5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748340548229839, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E7AF57065B7C5931.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748340548229893, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548230040, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548230306, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748340548230616, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548230774, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548231116, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748340548231228, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548231352, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548231543, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548232607, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548233245, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548233950, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548234666, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548235277, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548235828, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548236790, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548237437, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548238122, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548238778, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548239434, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548240045, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548240624, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548241220, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548241857, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548242471, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548243128, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548243438, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548243823, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748340548244032, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748340548244646, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548244909, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548245254, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748340548245508, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548245632, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748340548246353, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548246529, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548247153, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548248155, "dur": 93757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548341913, "dur": 3051, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548344965, "dur": 74502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548419468, "dur": 1739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748340548421208, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748340548421290, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548214801, "dur": 14330, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548229138, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_05B0F0D03FCBB381.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548229323, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphToolsFoundationModule.dll_E0D8DECCDDFDC907.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548229394, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_53867E5875A6F0D7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548229487, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548229549, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_BA45DE208F536ABF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548229605, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548229885, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_506DB266CFE78E16.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548230042, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_9C70EFD7FB48F931.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548230192, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_6B5854E2366B9BC5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548230307, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748340548230783, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548231073, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748340548231205, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548231267, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748340548231582, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548232486, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548233192, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548233767, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548234370, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548234912, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548235393, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548235856, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548236721, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548237288, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548237827, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548238372, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548238917, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548239443, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548239949, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548240446, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548240931, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548241386, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548241844, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548242414, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548243065, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548243446, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548243819, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548243992, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748340548244510, "dur": 804, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548245320, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548245784, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748340548246292, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548246383, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548246488, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548246628, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748340548247061, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548247155, "dur": 999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548248154, "dur": 1634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548249793, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748340548249914, "dur": 160366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548410286, "dur": 2609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748340548412897, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548412991, "dur": 1683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748340548414675, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548415218, "dur": 1792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748340548417011, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548417148, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748340548419521, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548420083, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548420260, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548420364, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748340548421274, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548214837, "dur": 14304, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548229419, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A598966EA4801EF0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748340548229612, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_42E86B1134F6755B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748340548229892, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_4655F3D030904137.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748340548230028, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548230121, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548230188, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_EF4D1401BBAB8F5F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748340548230300, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_EF4D1401BBAB8F5F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748340548230502, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748340548230595, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748340548230826, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748340548231116, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748340548231553, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548231828, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548232667, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548233331, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548233889, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548234440, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548234932, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548235394, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548235870, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548236927, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548237564, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548238268, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548238977, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548239642, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548240270, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548240909, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548241544, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548242290, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548242903, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548243431, "dur": 1593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548245024, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748340548245789, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548245856, "dur": 930, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548246791, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748340548246954, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748340548247242, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548247304, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548248154, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548248375, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748340548248523, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748340548248862, "dur": 161408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548410273, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748340548412918, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548413036, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748340548415423, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748340548417531, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748340548420022, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548420281, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748340548421022, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548214902, "dur": 14361, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548229273, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_1D75AFFA29B5BF7E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548229390, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D6E4F08340866AC0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548229487, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548229542, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_6B82B0725F9CED98.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548229603, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548229657, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_6B82B0725F9CED98.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548229778, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_121A3B12C200E7D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548229897, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548229990, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_75DB57AF5A91ABE9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548230041, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548230514, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748340548230635, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748340548230935, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548231189, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548231612, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548232655, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548233297, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548233896, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548234434, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548234916, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548235415, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548235956, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548236983, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548237613, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548238163, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548238716, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548239249, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548239743, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548240249, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548240751, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548241281, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548242140, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548242671, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548242899, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548243444, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548243817, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548244039, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748340548245891, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548246550, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548246675, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748340548247626, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748340548247755, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748340548248033, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548248105, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548248170, "dur": 162158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548410329, "dur": 2530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748340548412860, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548412942, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748340548415248, "dur": 1647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548416899, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748340548419302, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548419733, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548420279, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748340548420890, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548214850, "dur": 14367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548229227, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53F913A2D98BAB17.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748340548229313, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548229377, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_86F9AED61AFECF03.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748340548229485, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_0969509CB68B46D0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748340548229538, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548229664, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_A3EC03901D399452.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748340548229930, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_6AD4432A5D58D44D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748340548230032, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548230302, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748340548230513, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748340548231085, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748340548231431, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548232108, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548232935, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548233702, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548234374, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548234949, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548235425, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548235928, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548236859, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548237381, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548237925, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548238481, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548238975, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548239448, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548239941, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548240444, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548240943, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548241378, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548241825, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548242308, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548242908, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548243451, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548243830, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748340548244146, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748340548244804, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548244912, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748340548245695, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548246210, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548246381, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548246486, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748340548246630, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748340548247026, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548247159, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548248161, "dur": 162117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548410280, "dur": 2530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748340548412811, "dur": 738, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548413555, "dur": 1692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748340548415248, "dur": 1787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548417040, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748340548419225, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548419573, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548419950, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548420179, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748340548420331, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548214933, "dur": 14339, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548229277, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D72317661D31BFC3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548229384, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2570560EC5A06014.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548229488, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548229554, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D63016D42DE4D5AB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548229604, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548229684, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_90276A1043EE9D08.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548229735, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548229830, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548229883, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_9D163880D30C3CD7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548230027, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548230327, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1748340548230756, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748340548230872, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748340548231138, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748340548231321, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548231556, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548232481, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548233136, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548233747, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548234314, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548234818, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548235278, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548235738, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548236537, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548237083, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548237673, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548238208, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548238729, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548239268, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548239792, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548240294, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548240746, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548241198, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548241650, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548242103, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548242561, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548243213, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548243466, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548243869, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548244111, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548244639, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548244855, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748340548245534, "dur": 838, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548246407, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548246496, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548247150, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548247313, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748340548247696, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548247757, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548247814, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548247936, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748340548248336, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548248448, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748340548248539, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748340548248892, "dur": 161395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548410292, "dur": 2405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748340548412699, "dur": 2355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548415060, "dur": 1970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748340548417031, "dur": 2454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548419492, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548419676, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548420278, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548420659, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748340548421207, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548215049, "dur": 14262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548229318, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4EE5B2D3243E9209.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748340548229390, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548229740, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548229886, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_3B9A9D6895E03D57.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748340548230039, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_972ACF76E26C641F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748340548230193, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_3753AE2E22DD17B6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748340548230303, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1748340548230437, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748340548230828, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1748340548231560, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548232614, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548233251, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548233866, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548234437, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548234959, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548235447, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548235910, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548236768, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548237283, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548237794, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548238365, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548238887, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548239397, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548239884, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548240442, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548241066, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548241546, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548242244, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548242711, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548242963, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548243432, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548243820, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748340548244049, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748340548244615, "dur": 1307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548245953, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548246061, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548246547, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548247180, "dur": 992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548248172, "dur": 162163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548410336, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748340548412861, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548412931, "dur": 2035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748340548414967, "dur": 1401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548416375, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TestTools.CodeCoverage.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748340548418542, "dur": 1013, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548419771, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548419864, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548420153, "dur": 838, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748340548420994, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548215081, "dur": 14356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548229444, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EE0873F98CBCE221.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548229611, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DEB62EA577175CBC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548229670, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548229728, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8F77B47F334DBC6E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548229875, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548230046, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_806007E770BBC073.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548230321, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748340548230614, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548230752, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548231089, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748340548231282, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1679197209529901891.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748340548232262, "dur": 1013, "ph": "X", "name": "File", "args": {"detail": "D:\\Program Files\\Unity 2023.2.20f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Core.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748340548231586, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548233418, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548234035, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548234606, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548235071, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548235975, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@16.0.6\\Editor\\Data\\Nodes\\Channel\\CombineNode.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748340548235547, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548236663, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548237234, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548237783, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548238353, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548238926, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548239443, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548239953, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548240494, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548241016, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548241507, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548241954, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548242439, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548243135, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548243471, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548243867, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548244095, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548244161, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548244371, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548244566, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548244770, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548245385, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548245575, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548245767, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548245882, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548246777, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548246878, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548247152, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548247340, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548247679, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548247755, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548247856, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548248317, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748340548248406, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548248668, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548248941, "dur": 161353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548410297, "dur": 4565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548414862, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548414947, "dur": 3396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548418344, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548418444, "dur": 2473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748340548420972, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748340548421128, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548215102, "dur": 14343, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548229488, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548229544, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5F0102BF5E36ECD9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748340548229671, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548229893, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_FE6DBD5EA10C8819.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748340548230035, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_AEE30FD8AED4A30F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748340548230321, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748340548230594, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748340548230795, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548231090, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748340548231573, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548232447, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548233127, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548233696, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548234241, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548234749, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548235223, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548235680, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548236517, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548237055, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548237598, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548238171, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548238708, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548239226, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548239716, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548240259, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548240697, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548241137, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548242014, "dur": 590, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\ResponseType.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748340548241604, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548242630, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548243044, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548243437, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548243843, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748340548244047, "dur": 670, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548244722, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748340548244894, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748340548245458, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548245627, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748340548245883, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748340548246408, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548246490, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548246552, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548247171, "dur": 980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548248182, "dur": 162131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548410315, "dur": 2868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1748340548413184, "dur": 5174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748340548418363, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1748340548420695, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548215212, "dur": 14266, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548229478, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_89A58821477B4B64.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548229603, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C4B56F23438E0002.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548229662, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548229736, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4EE610E5975B8F5E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548229883, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548229984, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_30D74B706633088C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548230039, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548230136, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548230324, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_536C5C0B91A36678.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548230544, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548230993, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548231269, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.BurstCompatibilityGen.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1748340548231547, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548232660, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548233297, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548233909, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548234439, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548234919, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548235394, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548235875, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548236722, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548237249, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548237812, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548238348, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548238865, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548239368, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548239869, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548240382, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548240839, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548241327, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548241792, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548242509, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548243042, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548243439, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548243815, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548244014, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1748340548244761, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548245568, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548245723, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1748340548246336, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548246494, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748340548246643, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548246789, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1748340548247227, "dur": 897, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548248163, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1748340548248545, "dur": 146, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548249118, "dur": 156730, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1748340548410270, "dur": 2561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1748340548412832, "dur": 2088, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548414925, "dur": 1935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1748340548416861, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548417427, "dur": 2085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1748340548419512, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548419728, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548419805, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548420001, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548420100, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548420168, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548420294, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748340548421276, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548215160, "dur": 14310, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548229476, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_5256E0C0E0C8AB16.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748340548229596, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7CCD38B1B40A46E4.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748340548229682, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_CAB4FFF2979F7143.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748340548229821, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548229874, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_D9C00AF42ECCF857.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748340548230040, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548230134, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548230279, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548230591, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1748340548230936, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548231277, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14811776502145285846.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1748340548231549, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548233106, "dur": 4327, "ph": "X", "name": "File", "args": {"detail": "D:\\Program Files\\Unity 2023.2.20f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-convert-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748340548232566, "dur": 4944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548237510, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548238064, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548238607, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548239196, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548239674, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548240183, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548240658, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548241121, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548241601, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548242161, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548242515, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548243105, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548243466, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548243839, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748340548244257, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548244339, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748340548245124, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548245495, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748340548245684, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548245748, "dur": 859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748340548246608, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548246721, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548247164, "dur": 987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548248183, "dur": 162114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548410332, "dur": 2462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1748340548412795, "dur": 1159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548413960, "dur": 1947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1748340548415907, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548416295, "dur": 2487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1748340548418783, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748340548419359, "dur": 1747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Performance.Profile-Analyzer.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1748340548421175, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548215129, "dur": 14332, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548229469, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_2F7642AE7DB6D2E5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548229606, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C7DB8F900ED8C07C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548229813, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4BED7EAE9083B733.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548229910, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_593B33304BFCDC54.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548230045, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_70D12B6385F8C2F2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548230142, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_8C8B21D65F12B755.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548230734, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548230890, "dur": 9578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1748340548240469, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548240588, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548241067, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548241516, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548242222, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548242508, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548242987, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548243491, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548243842, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548244084, "dur": 1021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1748340548245106, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548245237, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1748340548246409, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548246507, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548247151, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748340548247316, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1748340548247692, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548247778, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548248167, "dur": 162109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548410278, "dur": 4614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1748340548414893, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548414977, "dur": 3422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1748340548418400, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748340548419019, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1748340548421291, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748340548426944, "dur": 1059, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 16720, "tid": 18, "ts": 1748340548443262, "dur": 1763, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 16720, "tid": 18, "ts": 1748340548445195, "dur": 1501, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 16720, "tid": 18, "ts": 1748340548438366, "dur": 9113, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}